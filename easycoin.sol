
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Address.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

interface IRouter {
    function factory() external pure returns (address);
    function WETH() external pure returns (address);
    function swapExactTokensForETHSupportingFeeOnTransferTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external;
}

interface IFactory {
    function createPair(address tokenA, address tokenB) external returns (address pair);
}

contract Easycoin is ERC20, Ownable{
    using Address for address payable;

    mapping (address user => bool status) public isExcludedFromFees;
    mapping (address user => bool status) public isBlacklisted;
    mapping (address user => uint256 timestamp) public lastTrade;

    IRouter public router;
    address public pair;
    address public marketingWallet = ******************************************;

    bool private swapping;
    bool public swapEnabled;
    bool public tradingEnabled;

    uint256 public swapThreshold = 500000 * 10**9;
    uint256 public maxWallet = ********* * 10**9;
    uint256 public maxTx = ********* * 10**9;
    uint256 public delay;

    struct Taxes {
        uint256 buy;
        uint256 sell;
        uint256 transfer;
    }

    Taxes public taxes = Taxes(10,30,0);

    modifier mutexLock() {
        if (!swapping) {
            swapping = true;
            _;
            swapping = false;
        }
    }
  
    constructor(address _router) ERC20("Easy Coin", "EASY") {
        _mint(msg.sender, *********00 * 10 ** 9);

        router = IRouter(_router);
        pair = IFactory(router.factory()).createPair(address(this), router.WETH());


        isExcludedFromFees[address(this)] = true;
        isExcludedFromFees[msg.sender] = true;
        isExcludedFromFees[marketingWallet] = true;

        _approve(address(this), address(router), type(uint256).max);
    }

    function decimals() public view virtual override returns (uint8) {
        return 9;
    }

    function _transfer(address sender, address recipient, uint256 amount) internal override {
        require(amount > 0, "Transfer amount must be greater than zero");

        if (swapping || isExcludedFromFees[sender] || isExcludedFromFees[recipient]) {
            super._transfer(sender, recipient, amount);
            return;
        }

        else{
            require(tradingEnabled, "Trading not enabled");
            require(!isBlacklisted[sender] && !isBlacklisted[recipient], "Blacklisted address");
            require(amount <= maxTx, "Transfer amount exceeds maxTx");

            if(sender != pair) {
                require(lastTrade[sender] + delay <= block.timestamp, "WAIT PLEASE");
                lastTrade[sender] = block.timestamp;
            }
            if(recipient != pair){
                require(balanceOf(recipient) + amount <= maxWallet, "Wallet limit exceeded");
                require(lastTrade[recipient] + delay <= block.timestamp, "WAIT PLEASE");
                lastTrade[recipient] = block.timestamp;
            }
        }
        
        uint256 fees;

        if(recipient == pair) fees = amount * taxes.sell / 100;
        else if(sender == pair) fees = amount * taxes.buy / 100;
        else fees = amount * taxes.transfer / 100; 

        if (swapEnabled && sender != pair && !swapping) swapFees();

        super._transfer(sender, recipient, amount - fees);
        if(fees > 0){
            super._transfer(sender, address(this), fees);
        }
    }

function swapFees() private mutexLock {
        uint256 contractBalance = balanceOf(address(this));
        if (contractBalance >= swapThreshold) {
            uint256 initialBalance = address(this).balance;
            swapTokensForEth(swapThreshold);
            uint256 deltaBalance = address(this).balance - initialBalance;
            payable(marketingWallet).sendValue(deltaBalance);
        }
    }

    function swapTokensForEth(uint256 tokenAmount) private {
        address[] memory path = new address[](2);
        path[0] = address(this);
        path[1] = router.WETH();

        router.swapExactTokensForETHSupportingFeeOnTransferTokens(
            tokenAmount,
            0,
            path,
            address(this),
            block.timestamp + 300
        );
    }

    function setSwapEnabled(bool status) external onlyOwner {
        swapEnabled = status;
    }

    function setSwapTreshhold(uint256 amount) external onlyOwner {
        require(amount > 0, "Threshold must be greater than 0");
        swapThreshold = amount * 10**9;
    }
    
    function setTaxes(uint256 _buyTax, uint256 _sellTax, uint256 _transferTax) external onlyOwner {
        require(_buyTax <= 69 && _sellTax <= 69 && _transferTax <= 69, "Taxes too high");
        taxes = Taxes (_buyTax, _sellTax, _transferTax);
    }
    
    function setRouterAndPair(address newRouter, address newPair) external onlyOwner{
        require(newRouter != address(0), "Router cannot be zero address");
        require(newPair != address(0), "Pair cannot be zero address");
        router = IRouter(newRouter);
        pair = newPair;
        _approve(address(this), address(newRouter), type(uint256).max);
    }
    
    function GTPepeGo() external onlyOwner{
        require(!tradingEnabled, "Trading already enabled");
        tradingEnabled = true;
        swapEnabled = true;
        taxes.transfer = 69;
    }
 
    function removeLimits() external onlyOwner{
        maxTx = totalSupply();
        maxWallet = totalSupply();
        taxes.transfer = 0;
    }

    function setDelay(uint256 time) external onlyOwner{
        delay = time;
    }

    function setLimits(uint256 _maxTx, uint256 _maxWallet) external onlyOwner{
        require(_maxTx > 0, "MaxTx must be greater than 0");
        require(_maxWallet > 0, "MaxWallet must be greater than 0");
        maxTx = _maxTx * 10**9;
        maxWallet = _maxWallet * 10**9;
    }
    
    function setMarketingWallet(address newWallet) external onlyOwner{
        require(newWallet != address(0), "Marketing wallet cannot be zero address");
        marketingWallet = newWallet;
    }

    function setIsExcludedFromFees(address _address, bool state) external onlyOwner {
        isExcludedFromFees[_address] = state;
    }
    
    function bulkIsExcludedFromFees(address[] memory accounts, bool state) external onlyOwner{
        require(accounts.length > 0, "Empty array");
        require(accounts.length <= 100, "Array too large");
        for(uint256 i = 0; i < accounts.length; i++){
            require(accounts[i] != address(0), "Cannot exclude zero address");
            isExcludedFromFees[accounts[i]] = state;
        }
    }

    function setBlacklist(address[] memory accounts, bool status) external onlyOwner{
        require(accounts.length > 0, "Empty array");
        require(accounts.length <= 100, "Array too large");
        for(uint256 i = 0; i < accounts.length; i++){
            require(accounts[i] != address(0), "Cannot blacklist zero address");
            require(accounts[i] != owner(), "Cannot blacklist owner");
            require(accounts[i] != marketingWallet, "Cannot blacklist marketing wallet");
            isBlacklisted[accounts[i]] = status;
        }
    }

    function rescueETH(uint256 weiAmount) external onlyOwner{
        require(weiAmount > 0, "Amount must be greater than 0");
        require(weiAmount <= address(this).balance, "Insufficient contract balance");
        payable(marketingWallet).sendValue(weiAmount);
    }

    function rescueERC20(address tokenAdd, uint256 amount) external onlyOwner{
        require(tokenAdd != address(0), "Token address cannot be zero");
        require(tokenAdd != address(this), "Cannot rescue own token");
        require(amount > 0, "Amount must be greater than 0");
        IERC20(tokenAdd).transfer(marketingWallet, amount);
    }

    // Public view functions
    function getTaxes() public view returns (uint256 buyTax, uint256 sellTax, uint256 transferTax) {
        return (taxes.buy, taxes.sell, taxes.transfer);
    }

    function getLimits() public view returns (uint256 maxTransaction, uint256 maxWalletSize) {
        return (maxTx, maxWallet);
    }

    function getSwapSettings() public view returns (uint256 threshold, bool enabled) {
        return (swapThreshold, swapEnabled);
    }

    function getTradingStatus() public view returns (bool enabled) {
        return tradingEnabled;
    }

    function getDelay() public view returns (uint256) {
        return delay;
    }

    function getContractBalance() public view returns (uint256 tokenBalance, uint256 ethBalance) {
        return (balanceOf(address(this)), address(this).balance);
    }

    function isAddressExcludedFromFees(address account) public view returns (bool) {
        return isExcludedFromFees[account];
    }

    function isAddressBlacklisted(address account) public view returns (bool) {
        return isBlacklisted[account];
    }

    function getLastTradeTime(address account) public view returns (uint256) {
        return lastTrade[account];
    }

    function canTrade(address account) public view returns (bool) {
        return lastTrade[account] + delay <= block.timestamp;
    }

    // fallbacks
    receive() external payable {}

}